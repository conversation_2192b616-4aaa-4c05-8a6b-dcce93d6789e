# Firebase 測試數據清理工具

## 📋 概述

這個工具用於安全地清理 Firebase 生產環境中的測試數據，確保不會誤刪真實用戶數據。

## 🎯 清理目標

### Firestore 數據
- **users** collection 中的測試用戶記錄
- **alertEvents** collection 中的測試事件記錄

### Realtime Database 數據
- **presence** 節點中的測試設備狀態
- **alertEvents** 節點中的測試事件數據

## 🔍 測試數據識別規則

### 自動識別模式
1. **設備 ID 模式**: `prod_test_device_*`
2. **FCM Token 模式**: `prod_test_fcm_token_*`
3. **用戶名稱模式**: `Production Test User` 或 `Test User`
4. **事件 ID 模式**: `event_*_*` (最近創建的)
5. **時間範圍**: 最近 24 小時內創建的測試數據

### 安全保護
- 只刪除明確標識為測試的數據
- 提供乾運行模式預覽
- 交互式確認機制
- 詳細的操作日誌

## 🚀 使用方法

### 基本用法

```bash
# 1. 乾運行模式（預覽要刪除的數據，不實際刪除）
pnpm cleanup-test-data

# 2. 執行實際清理
pnpm cleanup-test-data --execute

# 3. 查看幫助信息
pnpm cleanup-test-data --help
```

### 高級選項

```bash
# 清理最近 12 小時的測試數據
pnpm cleanup-test-data --execute --time-range 12

# 非交互模式（跳過確認，適合自動化腳本）
pnpm cleanup-test-data --execute --no-interactive

# 只預覽特定時間範圍的數據
pnpm cleanup-test-data --dry-run --time-range 6
```

### 命令行選項

| 選項 | 簡寫 | 說明 | 默認值 |
|------|------|------|--------|
| `--execute` | `-e` | 執行實際清理 | false (乾運行) |
| `--dry-run` | `-d` | 乾運行模式，只預覽 | true |
| `--no-interactive` | `-n` | 非交互模式 | false |
| `--time-range <小時>` | `-t` | 指定時間範圍 | 24 小時 |
| `--help` | `-h` | 顯示幫助信息 | - |

## 📊 輸出示例

### 乾運行模式輸出
```
🧹 Firebase 生產環境測試數據清理工具
=====================================
模式: 🔍 乾運行（預覽模式）
項目: qmnoti (生產環境)
時間範圍: 24 小時內

🔍 掃描 Firestore 用戶數據...
   找到 2 個測試用戶記錄
🔍 掃描 Firestore 事件數據...
   找到 1 個測試事件記錄
🔍 掃描 Realtime Database 數據...
   找到 2 個測試 presence 記錄
   找到 1 個測試 alertEvents 記錄

📊 測試數據摘要:
=====================================

🗃️  Firestore 用戶 (2 個):
   - prod_test_device_1749418361376 (Production Test User) - 匹配測試模式

📅 Firestore 事件 (1 個):
   - event_mbo6iwco_uuhkov (Production Test User) - 匹配測試模式

👥 Realtime Database Presence (2 個):
   - prod_test_device_1749418361376 - 匹配測試設備 ID

🔔 Realtime Database Events (1 個):
   - event_mbo6iwco_uuhkov - 匹配測試模式或最近創建

📊 總計: 6 個測試數據記錄
=====================================

⚠️  這是乾運行模式，沒有實際刪除任何數據
   要執行實際清理，請使用: pnpm cleanup-test-data --execute
```

### 實際清理輸出
```
🧹 Firebase 生產環境測試數據清理工具
=====================================
模式: 🗑️  實際清理模式
項目: qmnoti (生產環境)

⚠️  確定要刪除這 6 個測試數據記錄嗎？這個操作無法撤銷！ (y/N): y

🗑️  開始清理 Firestore 數據...
   ✅ 已刪除用戶: prod_test_device_1749418361376
   ✅ 已刪除事件: event_mbo6iwco_uuhkov
✅ Firestore 清理完成: 2 個用戶, 1 個事件

🗑️  開始清理 Realtime Database 數據...
   ✅ 已刪除 presence: prod_test_device_1749418361376
   ✅ 已刪除 alertEvent: event_mbo6iwco_uuhkov
✅ Realtime Database 清理完成: 2 個 presence, 1 個事件

📋 清理報告
=====================================
時間: 2025-01-27T14:30:00.000Z
模式: 實際清理
時間範圍: 24 小時

清理統計:
  - Firestore 用戶: 2
  - Firestore 事件: 1
  - Realtime Presence: 2
  - Realtime Events: 1
  - 總計: 6

✅ 清理操作已完成
=====================================
```

## ⚠️ 安全注意事項

### 使用前檢查
1. **確認環境**: 確保連接到正確的 Firebase 項目
2. **備份重要數據**: 雖然只刪除測試數據，但建議先備份
3. **乾運行測試**: 先使用乾運行模式檢查要刪除的數據
4. **權限確認**: 確保有足夠的 Firebase 權限執行刪除操作

### 最佳實踐
1. **定期清理**: 建議每週運行一次清理
2. **時間範圍**: 使用適當的時間範圍避免誤刪
3. **監控日誌**: 檢查清理日誌確保操作正確
4. **測試驗證**: 清理後驗證應用功能正常

## 🔧 故障排除

### 常見問題

1. **權限錯誤**
   ```
   Error: Missing or insufficient permissions
   ```
   **解決方案**: 確保 Firebase 帳戶有 Firestore 和 Realtime Database 的寫入權限

2. **網路連接問題**
   ```
   Error: Network request failed
   ```
   **解決方案**: 檢查網路連接，確保能訪問 Firebase 服務

3. **數據不存在**
   ```
   找到 0 個測試數據記錄
   ```
   **解決方案**: 正常情況，表示沒有需要清理的測試數據

### 手動驗證

清理完成後，可以手動檢查：

1. **Firebase Console**: 
   - Firestore: https://console.firebase.google.com/project/qmnoti/firestore
   - Realtime Database: https://console.firebase.google.com/project/qmnoti/database

2. **命令行驗證**:
   ```bash
   # 再次運行乾運行模式檢查
   pnpm cleanup-test-data --dry-run
   ```

## 📞 支援

如果遇到問題：
1. 檢查 Firebase Console 中的錯誤日誌
2. 確認網路連接和權限設置
3. 使用乾運行模式調試問題
4. 查看腳本輸出的詳細錯誤信息
