# Firebase 測試數據隔離問題修復報告

## 問題描述

測試數據被錯誤地插入到生產環境的 'users' collection 中，而不是預期的 'test_users' collection 中，違反了數據隔離原則。

## 根本原因分析

### 1. Cloud Functions 中的硬編碼集合名稱
- `initializeStaffData` 函數直接使用 `firestore.collection("users")`
- `acknowledgeAlert` 和 `cancelAlert` 函數直接使用 `firestore.collection("alertEvents")`
- Realtime Database 路徑也存在硬編碼問題

### 2. 環境檢測邏輯不一致
- 客戶端：`NODE_ENV !== 'production'` → 開發環境
- Cloud Functions：`NODE_ENV === 'development'` → 開發環境
- 兩種邏輯相互矛盾，導致環境判斷錯誤

### 3. Cloud Functions 環境變數配置缺失
- Cloud Functions 沒有獨立的 .env 文件
- 環境變數可能沒有正確傳遞到 Cloud Functions

## 修復措施

### 1. 修復 Cloud Functions 硬編碼問題

#### 更新集合名稱管理函數
```typescript
function getCollectionName(baseCollectionName: string): string {
  if (isDevelopment) {
    const testCollections: { [key: string]: string } = {
      'users': process.env.TEST_FIRESTORE_USERS_COLLECTION || 'test_users',
      'alertEvents': process.env.TEST_FIRESTORE_EVENTS_COLLECTION || 'test_alertEvents',
      'groups': process.env.TEST_FIRESTORE_GROUPS_COLLECTION || 'test_groups',
      'staff': process.env.TEST_FIRESTORE_STAFF_COLLECTION || 'test_staff'
    };
    return testCollections[baseCollectionName] || `test_${baseCollectionName}`;
  }
  return baseCollectionName;
}
```

#### 修復的函數
1. **initializeStaffData**：使用 `getCollectionName("users")`
2. **createAlert**：修復 Realtime Database 路徑
3. **acknowledgeAlert**：使用環境相應的集合和路徑
4. **cancelAlert**：使用環境相應的集合和路徑
5. **updateNotificationStats**：動態生成觸發器路徑

### 2. 創建 Cloud Functions 環境配置

創建 `functions/.env` 文件：
```env
NODE_ENV=development
TEST_FIRESTORE_USERS_COLLECTION=test_users
TEST_FIRESTORE_EVENTS_COLLECTION=test_alertEvents
TEST_FIRESTORE_GROUPS_COLLECTION=test_groups
TEST_FIRESTORE_STAFF_COLLECTION=test_staff
TEST_REALTIME_PRESENCE_PATH=test_presence
TEST_REALTIME_EVENTS_PATH=test_alertEvents
TEST_REALTIME_STATS_PATH=test_stats
```

### 3. 創建驗證腳本

新增 `scripts/verifyDataIsolation.ts` 腳本：
- 驗證環境配置是否正確
- 檢查 Firestore 集合隔離
- 檢查 Realtime Database 路徑隔離
- 警告生產集合中的測試數據污染

### 4. 更新 package.json

新增驗證腳本：
```json
"verify-data-isolation": "npx tsx ./scripts/verifyDataIsolation.ts"
```

## 驗證步驟

### 1. 運行驗證腳本
```bash
npm run verify-data-isolation
```

### 2. 檢查環境配置
確認輸出顯示：
- NODE_ENV: development
- 是否開發環境: true
- 集合名稱都有 test_ 前綴

### 3. 測試 Cloud Functions
```bash
npm run test-firebase-strategy
```

### 4. 清理測試數據
```bash
npm run cleanup-test-data
```

## 預期結果

修復後，所有測試數據應該：
1. **Firestore 集合**：寫入 `test_users`、`test_alertEvents` 等測試集合
2. **Realtime Database**：使用 `test_presence`、`test_alertEvents` 等測試路徑
3. **生產集合**：保持乾淨，不被測試數據污染

## 後續建議

### 1. 代碼審查檢查點
- 禁止在 Cloud Functions 中硬編碼集合名稱
- 必須使用 `getCollectionName()` 和 `getRealtimePath()` 函數
- 新增的 Cloud Functions 必須支持環境隔離

### 2. 自動化測試
- 在 CI/CD 中加入數據隔離驗證
- 定期運行清理腳本
- 監控生產集合是否有測試數據污染

### 3. 文檔更新
- 更新開發者指南
- 添加環境配置說明
- 記錄最佳實踐

## 風險評估

### 修復前風險
- **高風險**：測試數據污染生產環境
- **中風險**：數據混亂，難以區分測試和生產數據
- **低風險**：影響生產環境性能

### 修復後風險
- **低風險**：Cloud Functions 部署可能需要重新配置環境變數
- **極低風險**：現有功能不受影響，只是改變數據存儲位置

## 總結

此次修復徹底解決了 Firebase 測試數據隔離問題，確保：
1. 測試數據嚴格隔離到 test_ 前綴的集合中
2. 生產數據不被測試數據污染
3. 環境切換邏輯統一且可靠
4. 提供完整的驗證和清理工具

修復完成後，開發團隊可以安全地進行測試，不用擔心影響生產環境數據。
