# Firebase 生產環境部署指南

## 🚀 概述

本指南將幫助您將 React Native 應用從開發環境（Firebase 模擬器）切換到生產環境（真實 Firebase 服務）。

## 📋 前置條件

### 1. Firebase 項目升級到 Blaze 計劃

**為什麼需要升級？**
- Cloud Functions 需要 Cloud Build API
- 只有 Blaze (pay-as-you-go) 計劃支持 Cloud Functions 部署

**升級步驟：**
1. 訪問 [Firebase Console](https://console.firebase.google.com/project/qmnoti/usage/details)
2. 點擊左側選單的「使用量和計費」
3. 點擊「修改計劃」
4. 選擇「Blaze (隨用隨付)」計劃
5. 設置預算警報（建議設置每月 $10-20 的警報）

**費用說明：**
- 免費額度：每月 200 萬次函數調用
- 超出免費額度後：每 100 萬次調用 $0.40
- 對於開發和小型應用，通常不會超出免費額度

## 🔄 環境切換

### 使用環境切換工具

```bash
# 切換到生產環境
pnpm switch-env production

# 切換到開發環境（模擬器）
pnpm switch-env development

# 查看當前環境狀態
pnpm switch-env status

# 顯示幫助信息
pnpm switch-env help
```

### 手動設置環境變數

如果需要手動設置，請編輯 `.env` 文件：

```env
# 生產環境
FIREBASE_ENV=production
NODE_ENV=production

# 開發環境
FIREBASE_ENV=emulator
NODE_ENV=development
```

## 🚀 部署 Cloud Functions

### 1. 確保代碼通過檢查

```bash
# 類型檢查
pnpm type-check

# 代碼風格檢查
pnpm lint

# 本地測試（模擬器環境）
pnpm cloud-functions-test
```

### 2. 部署到生產環境

```bash
# 部署所有 Functions
firebase deploy --only functions

# 部署特定 Function
firebase deploy --only functions:registerUser

# 查看部署狀態
firebase functions:list
```

### 3. 驗證部署

```bash
# 測試生產環境配置
pnpm production-test

# 檢查 Functions 日誌
firebase functions:log
```

## 🔧 配置安全規則

### Firestore 安全規則

編輯 `firestore.rules` 文件：

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // 用戶數據：只允許讀取，寫入通過 Cloud Functions
    match /users/{userId} {
      allow read: if true;
      allow write: if false; // 只能通過 Cloud Functions 寫入
    }
    
    // 通知事件：只允許讀取
    match /events/{eventId} {
      allow read: if true;
      allow write: if false; // 只能通過 Cloud Functions 寫入
    }
  }
}
```

### Realtime Database 安全規則

編輯 `database.rules.json` 文件：

```json
{
  "rules": {
    "users": {
      ".read": true,
      ".write": false
    },
    "events": {
      ".read": true,
      ".write": false
    },
    "notifications": {
      ".read": true,
      ".write": false
    }
  }
}
```

### 部署安全規則

```bash
# 部署 Firestore 規則
firebase deploy --only firestore:rules

# 部署 Database 規則
firebase deploy --only database

# 部署所有規則
firebase deploy --only firestore:rules,database
```

## 📱 React Native 應用配置

### 1. 確保環境正確

```bash
# 檢查當前環境
pnpm switch-env status

# 如果不是生產環境，切換到生產環境
pnpm switch-env production
```

### 2. 重新啟動應用

```bash
# 清除緩存並重新啟動
npx expo start --clear

# 或者重新構建
npx expo run:ios
npx expo run:android
```

## 🧪 測試生產環境

### 1. 功能測試清單

- [ ] Profile 頁面用戶註冊功能
- [ ] FCM Token 獲取和推播通知
- [ ] 創建通知事件
- [ ] 確認和取消通知
- [ ] 用戶數據同步

### 2. 測試腳本

```bash
# 測試生產環境配置
pnpm production-test

# 測試 FCM Token 獲取
pnpm fcm-token-test

# 檢查 Firebase 連接
pnpm firebase-connection-test
```

### 3. 真實設備測試

1. 在真實 iOS/Android 設備上安裝應用
2. 測試用戶註冊流程
3. 測試推播通知功能
4. 驗證數據同步

## 🔍 故障排除

### 常見問題

1. **"not-found" 錯誤**
   - 確認 Cloud Functions 已部署
   - 檢查 Functions 區域設置 (asia-east1)
   - 驗證環境變數設置

2. **FCM Token 獲取失敗**
   - 確認在真實設備上測試
   - 檢查推播通知權限
   - 驗證 Firebase 配置

3. **權限錯誤**
   - 檢查 Firestore 和 Database 安全規則
   - 確認 Cloud Functions 有適當權限

### 日誌檢查

```bash
# 查看 Functions 日誌
firebase functions:log

# 查看特定 Function 日誌
firebase functions:log --only registerUser

# 實時日誌
firebase functions:log --follow
```

## 📊 監控和維護

### 1. 設置監控

- 在 Firebase Console 中設置性能監控
- 配置錯誤報告
- 設置使用量警報

### 2. 定期檢查

- 每週檢查 Functions 執行狀況
- 監控 Firestore 和 Database 使用量
- 檢查安全規則是否需要更新

## 🔄 回滾到開發環境

如果需要回到開發環境：

```bash
# 切換到開發環境
pnpm switch-env development

# 啟動模擬器
firebase emulators:start

# 重新啟動應用
npx expo start --clear
```

## 📞 支援

如果遇到問題：

1. 檢查 Firebase Console 中的錯誤日誌
2. 運行診斷腳本：`pnpm production-test`
3. 查看 [Firebase 文檔](https://firebase.google.com/docs)
4. 檢查 [React Native Firebase 文檔](https://rnfirebase.io/)
