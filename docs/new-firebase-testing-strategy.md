# 新Firebase測試策略

## 概述

本文檔描述了QMNotiAugment項目採用的新Firebase測試策略，該策略解決了Firebase模擬器的複雜性和連接問題，提供了更穩定、更接近生產環境的測試方案。

## 策略核心

### 基本原理

1. **環境判斷**：通過 `NODE_ENV` 環境變數判斷當前環境
2. **集合隔離**：在開發環境使用測試集合前綴，確保數據隔離
3. **真實服務**：始終使用真實Firebase服務，避免模擬器問題
4. **自動清理**：支持測試數據的自動清理機制

### 環境配置

#### 開發環境 (NODE_ENV=development)
- 使用真實Firebase服務
- 自動添加測試集合前綴
- 支持測試數據自動清理
- 接受測試FCM Token

#### 生產環境 (NODE_ENV=production)
- 使用真實Firebase服務
- 使用正式集合名稱
- 嚴格的FCM Token驗證
- 不允許測試數據

## 配置文件

### .env 文件配置

```bash
# 環境設置
NODE_ENV=development

# 測試集合配置
TEST_FIRESTORE_COLLECTION_PREFIX=test_
TEST_REALTIME_DB_PATH_PREFIX=test_
TEST_DEVICE_ID_PREFIX=dev_test_

# 測試數據管理
AUTO_CLEANUP_TEST_DATA=true
TEST_DATA_RETENTION_DAYS=7
```

### 集合映射

| 基礎集合名 | 開發環境 | 生產環境 |
|-----------|---------|---------|
| users | test_users | users |
| alertEvents | test_alertEvents | alertEvents |
| groups | test_groups | groups |
| staff | test_staff | staff |

### Realtime Database路徑映射

| 基礎路徑 | 開發環境 | 生產環境 |
|---------|---------|---------|
| presence | test_presence | presence |
| alertEvents | test_alertEvents | alertEvents |
| stats | test_stats | stats |

## 代碼實現

### 客戶端配置 (firebaseConfig.ts)

```typescript
// 環境檢測
const isDevelopment = process.env.NODE_ENV === 'development';

// 統一使用真實Firebase服務
const functions = getFunctions(app, 'asia-east1');
```

### 環境管理工具 (utils/firebaseEnvironment.ts)

```typescript
// 獲取集合名稱
export const getFirebaseCollectionName = (baseCollectionName: string): string => {
  return getFirebaseEnvironmentManager().getCollectionName(baseCollectionName);
};

// 獲取Realtime Database路徑
export const getFirebaseRealtimePath = (basePathName: string): string => {
  return getFirebaseEnvironmentManager().getRealtimePath(basePathName);
};
```

### Cloud Functions實現

```typescript
// 環境檢測
const isDevelopment = process.env.NODE_ENV === 'development';

// 集合名稱管理
function getCollectionName(baseCollectionName: string): string {
  if (isDevelopment) {
    const prefix = process.env.TEST_FIRESTORE_COLLECTION_PREFIX || "test_";
    return `${prefix}${baseCollectionName}`;
  }
  return baseCollectionName;
}

// 使用示例
const usersCollection = getCollectionName("users");
await firestore.collection(usersCollection).doc(deviceID).set(userData);
```

## FCM Token處理

### 開發環境
- 接受測試token（`test_fcm_token_`前綴）
- 接受真實設備token
- 跳過實際FCM發送，模擬成功

### 生產環境
- 嚴格驗證FCM Token格式
- 拒絕測試token
- 實際發送FCM推播

## 測試腳本

### 主要測試腳本

```bash
# 測試新Firebase策略
pnpm test-firebase-strategy

# 生產環境測試
pnpm production-test

# 生產環境Functions測試
pnpm production-functions-test

# 清理測試集合（乾運行）
pnpm cleanup-test-collections

# 清理測試集合（實際執行）
pnpm cleanup-test-collections-execute
```

### 測試內容

1. **環境檢測測試**
2. **集合名稱生成測試**
3. **用戶註冊功能測試**
4. **數據隔離驗證**
5. **自動清理測試**

## 優勢

### 相比Firebase模擬器的優勢

1. **穩定性**：無需管理模擬器進程，避免端口衝突
2. **真實性**：使用真實Firebase服務，更接近生產環境
3. **簡單性**：環境切換只需修改NODE_ENV
4. **可靠性**：減少網絡連接問題和配置錯誤
5. **效率**：無需等待模擬器啟動，測試更快

### 數據安全

1. **完全隔離**：測試數據與生產數據完全分離，使用獨立的測試集合
2. **集合級清理**：支持整個測試集合的批量清理，更高效安全
3. **簡化標識**：測試設備ID使用`test_device_`前綴，清晰易識別
4. **權限控制**：生產環境拒絕測試數據，開發環境接受測試token

## 使用指南

### 開發環境設置

1. 設置環境變數：
   ```bash
   export NODE_ENV=development
   ```

2. 運行測試：
   ```bash
   pnpm test-firebase-strategy
   ```

3. 檢查測試集合：
   - Firestore: `test_users`, `test_alertEvents`
   - Realtime DB: `test_presence`, `test_alertEvents`

### 生產環境部署

1. 設置環境變數：
   ```bash
   export NODE_ENV=production
   ```

2. 部署Functions：
   ```bash
   cd functions && pnpm deploy
   ```

3. 驗證部署：
   ```bash
   pnpm production-test
   ```

## 故障排除

### 常見問題

1. **集合名稱錯誤**
   - 檢查NODE_ENV設置
   - 驗證環境變數載入

2. **權限錯誤**
   - 確認Firebase項目權限
   - 檢查服務帳戶配置

3. **數據未隔離**
   - 驗證集合前綴配置
   - 檢查環境檢測邏輯

### 調試工具

```bash
# 檢查環境配置
pnpm test-firebase-strategy

# 查看集合狀態
# 在Firebase Console中檢查test_前綴的集合
```

## 遷移指南

### 從模擬器策略遷移

1. **清理舊文件**：移除模擬器相關腳本和配置
2. **更新配置**：修改.env文件使用新配置
3. **測試驗證**：運行新測試腳本確認功能
4. **更新文檔**：更新團隊開發文檔

### 注意事項

- 確保所有團隊成員了解新策略
- 更新CI/CD流程以使用新的測試方法
- 定期清理測試數據避免累積

## 總結

新的Firebase測試策略通過使用真實Firebase服務和集合前綴實現了數據隔離，解決了模擬器的複雜性問題，提供了更穩定、更可靠的開發和測試環境。這種方法既保證了開發效率，又確保了數據安全，是一個更適合生產環境的解決方案。
