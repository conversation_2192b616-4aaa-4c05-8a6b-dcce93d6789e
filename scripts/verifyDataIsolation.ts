/**
 * 數據隔離驗證腳本
 * 驗證測試數據是否正確隔離到測試集合中
 */

import { collection, getDocs, query, limit } from 'firebase/firestore';
import { ref, get } from 'firebase/database';
import { firestoreDB, realtimeDB } from '../firebaseConfig';
import { 
  getFirebaseCollectionName, 
  getFirebaseRealtimePath,
  isFirebaseDevelopment,
  getFirebaseEnvironmentManager
} from '../utils/firebaseEnvironment';

// 確保Node.js環境變數可用
declare const process: {
  env: { [key: string]: string | undefined };
  exit: (code?: number) => never;
};

/**
 * 驗證環境配置
 */
async function verifyEnvironmentConfig(): Promise<void> {
  console.log('🔍 驗證環境配置...\n');
  
  const envManager = getFirebaseEnvironmentManager();
  const config = envManager.getEnvironmentConfig();
  
  console.log('📊 當前環境配置:');
  console.log(`   環境: ${config.name}`);
  console.log(`   NODE_ENV: ${process.env.NODE_ENV}`);
  console.log(`   是否開發環境: ${config.isDevelopment}`);
  console.log('');
  
  console.log('📁 Firestore 集合名稱:');
  Object.entries(config.collections).forEach(([key, value]) => {
    console.log(`   ${key}: ${value}`);
  });
  console.log('');
  
  console.log('📈 Realtime Database 路徑:');
  Object.entries(config.realtimePaths).forEach(([key, value]) => {
    console.log(`   ${key}: ${value}`);
  });
  console.log('');
}

/**
 * 檢查 Firestore 集合是否正確隔離
 */
async function checkFirestoreIsolation(): Promise<void> {
  console.log('🔍 檢查 Firestore 集合隔離...\n');
  
  const collections = ['users', 'alertEvents', 'groups', 'staff'] as const;
  
  for (const collectionName of collections) {
    const testCollectionName = getFirebaseCollectionName(collectionName);
    const prodCollectionName = collectionName;
    
    console.log(`📊 檢查集合: ${collectionName}`);
    console.log(`   測試集合名稱: ${testCollectionName}`);
    console.log(`   生產集合名稱: ${prodCollectionName}`);
    
    try {
      // 檢查測試集合
      const testCollectionRef = collection(firestoreDB, testCollectionName);
      const testSnapshot = await getDocs(query(testCollectionRef, limit(5)));
      console.log(`   測試集合文檔數量: ${testSnapshot.size}`);
      
      // 檢查生產集合（僅在開發環境下檢查）
      if (isFirebaseDevelopment()) {
        const prodCollectionRef = collection(firestoreDB, prodCollectionName);
        const prodSnapshot = await getDocs(query(prodCollectionRef, limit(5)));
        console.log(`   生產集合文檔數量: ${prodSnapshot.size}`);
        
        // 警告：如果生產集合有新的測試數據
        if (prodSnapshot.size > 0) {
          console.log(`   ⚠️  警告: 生產集合 ${prodCollectionName} 中有數據，請檢查是否為測試數據污染`);
        }
      }
    } catch (error) {
      console.log(`   ❌ 檢查失敗: ${error}`);
    }
    
    console.log('');
  }
}

/**
 * 檢查 Realtime Database 路徑是否正確隔離
 */
async function checkRealtimeDatabaseIsolation(): Promise<void> {
  console.log('🔍 檢查 Realtime Database 路徑隔離...\n');
  
  const paths = ['presence', 'alertEvents', 'stats'] as const;
  
  for (const pathName of paths) {
    const testPath = getFirebaseRealtimePath(pathName);
    const prodPath = pathName;
    
    console.log(`📈 檢查路徑: ${pathName}`);
    console.log(`   測試路徑: ${testPath}`);
    console.log(`   生產路徑: ${prodPath}`);
    
    try {
      // 檢查測試路徑
      const testRef = ref(realtimeDB, testPath);
      const testSnapshot = await get(testRef);
      console.log(`   測試路徑存在: ${testSnapshot.exists()}`);
      if (testSnapshot.exists()) {
        const data = testSnapshot.val();
        const dataSize = typeof data === 'object' ? Object.keys(data).length : 1;
        console.log(`   測試路徑數據項數: ${dataSize}`);
      }
      
      // 檢查生產路徑（僅在開發環境下檢查）
      if (isFirebaseDevelopment()) {
        const prodRef = ref(realtimeDB, prodPath);
        const prodSnapshot = await get(prodRef);
        console.log(`   生產路徑存在: ${prodSnapshot.exists()}`);
        if (prodSnapshot.exists()) {
          const data = prodSnapshot.val();
          const dataSize = typeof data === 'object' ? Object.keys(data).length : 1;
          console.log(`   生產路徑數據項數: ${dataSize}`);
          console.log(`   ⚠️  警告: 生產路徑 ${prodPath} 中有數據，請檢查是否為測試數據污染`);
        }
      }
    } catch (error) {
      console.log(`   ❌ 檢查失敗: ${error}`);
    }
    
    console.log('');
  }
}

/**
 * 主驗證函數
 */
async function main(): Promise<void> {
  console.log('🚀 開始驗證 Firebase 數據隔離配置...\n');
  
  try {
    await verifyEnvironmentConfig();
    await checkFirestoreIsolation();
    await checkRealtimeDatabaseIsolation();
    
    console.log('✅ 數據隔離驗證完成');
    
    if (isFirebaseDevelopment()) {
      console.log('\n💡 提示:');
      console.log('   - 當前為開發環境，應該使用測試集合');
      console.log('   - 如果生產集合中有數據，請檢查是否為測試數據污染');
      console.log('   - 運行清理腳本清除測試數據: npm run cleanup-test-data');
    } else {
      console.log('\n🚀 當前為生產環境，使用正式集合');
    }
    
  } catch (error) {
    console.error('❌ 驗證過程中發生錯誤:', error);
    process.exit(1);
  }
}

// 執行驗證
if (require.main === module) {
  main();
}

export { main as verifyDataIsolation };
