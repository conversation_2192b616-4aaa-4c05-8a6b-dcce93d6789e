/**
 * 真實 Firebase + 測試集合 測試腳本
 * 使用真實 Firebase 服務但在測試集合中進行操作
 */

import { httpsCallable } from 'firebase/functions';
import { functions } from '../firebaseConfig';
import {
  validateTestEnvironment,
  displayTestEnvironmentInfo,
  createTestUserData,
  createTestAlertData,
  generateTestDeviceID
} from '../utils/testCollectionManager';

// 確保Node.js環境變數可用
declare const process: {
  env: { [key: string]: string | undefined };
  exit: (code?: number) => never;
};

declare const require: {
  main: any;
};

declare const module: any;

// 類型定義
interface RegisterUserRequest {
  deviceID: string;
  nickname: string;
  fcmToken: string;
  name?: string;
  role?: string;
  initials?: string;
  color?: string;
  phoneNumber?: string;
  avatar?: string;
}

interface CreateAlertRequest {
  initiatorDeviceID: string;
  caseType: string;
  motherInitial: string;
  bedNumber?: string;
  designatedWard: string;
  clinicalNotes?: string;
  recipientDeviceIDs: string[];
}

/**
 * 主測試函數
 */
async function testRealFirebaseWithTestCollections(): Promise<void> {
  console.log('🧪 開始測試真實 Firebase + 測試集合模式...\n');

  // 用於存儲測試過程中的數據
  let testEventID: string | undefined;

  try {
    // 驗證測試環境配置
    validateTestEnvironment();
    displayTestEnvironmentInfo();

    // 3. 測試 Cloud Functions
    console.log('3. 🔗 測試 Cloud Functions 連接');

    // 測試 registerUser
    console.log('   測試 registerUser 函數...');
    const registerUserFunction = httpsCallable<RegisterUserRequest, any>(functions, 'registerUser');

    const testUserData = createTestUserData({
      nickname: 'Real Firebase 測試用戶',
      name: 'Real Firebase 測試用戶姓名',
      initials: 'RF',
    });

    const registerResult = await registerUserFunction(testUserData);

    console.log('   ✅ registerUser 測試成功');
    console.log(`   📊 結果: ${JSON.stringify(registerResult.data, null, 2)}`);
    console.log('');

    // 保存測試設備ID供後續使用
    const testDeviceID = testUserData.deviceID;

    // 4. 測試 createAlert（如果 registerUser 成功）
    if (registerResult.data?.success) {
      console.log('4. 📤 測試 createAlert 函數');
      const createAlertFunction = httpsCallable<CreateAlertRequest, any>(functions, 'createAlert');
      
      const alertResult = await createAlertFunction({
        initiatorDeviceID: testDeviceID,
        caseType: 'mother_baby_transfer',
        motherInitial: 'A',
        bedNumber: '101',
        designatedWard: 'NICU',
        clinicalNotes: '測試通知內容',
        recipientDeviceIDs: [testDeviceID], // 發送給自己進行測試
      });
      
      console.log('   ✅ createAlert 測試成功');
      console.log(`   📊 結果: ${JSON.stringify(alertResult.data, null, 2)}`);
      console.log('');

      // 保存事件 ID 供後續測試
      testEventID = alertResult.data?.eventID;
    }

    // 5. 測試 acknowledgeAlert（如果有事件 ID）
    if (testEventID) {
      console.log('5. ✅ 測試 acknowledgeAlert 函數');
      const acknowledgeAlertFunction = httpsCallable(functions, 'acknowledgeAlert');

      const acknowledgeResult = await acknowledgeAlertFunction({
        eventID: testEventID,
        recipientDeviceID: testDeviceID,
      });

      console.log('   ✅ acknowledgeAlert 測試成功');
      console.log(`   📊 結果: ${JSON.stringify(acknowledgeResult.data, null, 2)}`);
      console.log('');
    }

    console.log('🎉 所有測試完成！\n');
    
    // 總結
    console.log('📊 測試總結:');
    console.log('- ✅ 使用真實 Firebase 服務避免了網絡連接問題');
    console.log('- ✅ 通過測試集合確保數據隔離');
    console.log('- ✅ 所有主要 Cloud Functions 正常工作');
    console.log('- ✅ 測試數據已標記，便於後續清理');
    
    console.log('\n🧹 數據清理:');
    console.log('- 測試數據使用了測試前綴，可以安全識別和清理');
    console.log('- 運行清理腳本: pnpm cleanup-test-data');
    
  } catch (error) {
    console.error('❌ 測試失敗:', error);
    
    console.log('\n🔍 故障排除:');
    console.log('1. 檢查網絡連接到 Firebase');
    console.log('2. 確認 Cloud Functions 已正確部署');
    console.log('3. 檢查 Firebase 項目權限設置');
    console.log('4. 驗證環境變數配置是否正確');
    
    process.exit(1);
  }
}

// 擴展 global 類型以支持測試變數
declare global {
  var testEventID: string;
}

// 執行測試
if (require.main === module) {
  testRealFirebaseWithTestCollections();
}

export { testRealFirebaseWithTestCollections }; 