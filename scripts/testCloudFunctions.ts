/**
 * Cloud Functions 測試腳本 (TypeScript 版本)
 * 用於驗證 Firebase Functions 服務是否正常工作
 * 注意：此腳本使用測試集合，確保不會污染生產數據
 */

import { registerUser, createAlert, getDeviceID, getFCMToken } from '../services/firebaseFunctions';
import {
  validateTestEnvironment,
  displayTestEnvironmentInfo,
  createTestUserData,
  createTestAlertData
} from '../utils/testCollectionManager';

// 確保Node.js環境變數可用
declare const process: {
  env: { [key: string]: string | undefined };
  exit: (code?: number) => never;
};

async function testCloudFunctions(): Promise<void> {
  console.log('🚀 開始測試 Cloud Functions 集成（使用測試集合）...\n');

  try {
    // 驗證測試環境配置
    validateTestEnvironment();
    displayTestEnvironmentInfo();

    // 測試獲取設備 ID
    console.log('1. 測試獲取設備 ID...');
    const deviceID = await getDeviceID();
    console.log(`✅ 設備 ID: ${deviceID}\n`);

    // 測試獲取 FCM Token
    console.log('2. 測試獲取 FCM Token...');
    const fcmToken = await getFCMToken();
    console.log(`✅ FCM Token: ${fcmToken ? fcmToken.substring(0, 50) + '...' : '空 (開發環境)'}\n`);

    // 測試用戶註冊（使用測試數據）
    console.log('3. 測試用戶註冊...');
    const testUserData = createTestUserData({
      nickname: 'Cloud Functions Test User',
      name: 'Cloud Functions Test User',
      initials: 'CF',
    });

    // 移除deviceID，讓registerUser函數自動獲取
    const { deviceID: _, ...userDataForRegister } = testUserData;

    const registerResult = await registerUser(userDataForRegister);
    console.log('✅ 註冊結果:', registerResult);
    console.log();

    // 測試創建通知（使用測試數據）
    console.log('4. 測試創建通知...');
    const testAlertData = createTestAlertData(
      deviceID, // 使用當前設備作為發起者
      [deviceID], // 發送給自己進行測試
      {
        motherInitial: 'CF',
        bedNumber: '123',
        designatedWard: 'Test Ward',
        clinicalNotes: 'Cloud Functions 測試通知',
      }
    );

    // 移除initiatorDeviceID，讓createAlert函數自動獲取
    const { initiatorDeviceID: __, ...alertDataForCreate } = testAlertData;

    const alertResult = await createAlert(alertDataForCreate);
    console.log('✅ 通知創建結果:', alertResult);
    console.log();

    console.log('🎉 所有測試完成！Cloud Functions 集成正常工作。');
    console.log('📊 所有測試數據都存儲在測試集合中，不會污染生產數據。');

  } catch (error) {
    console.error('❌ 測試失敗:', error);
    console.error('\n可能的解決方案:');
    console.error('1. 確保 Firebase Functions 已正確部署');
    console.error('2. 檢查網路連接');
    console.error('3. 驗證 Firebase 配置是否正確');
    console.error('4. 確保在真實設備上運行測試（某些功能需要設備支持）');
    console.error('5. 檢查測試集合配置是否正確');
    process.exit(1);
  }
}

// 直接执行测试函数
testCloudFunctions().catch((error) => {
  console.error('测试脚本执行失败:', error);
  process.exit(1);
});

export { testCloudFunctions }; 