/**
 * 測試集合清理腳本
 * 用於清理整個測試集合，確保測試數據與生產數據完全隔離
 */

import { initializeApp } from 'firebase/app';
import { getFirestore, collection, getDocs, deleteDoc, doc } from 'firebase/firestore';
import { getDatabase, ref, get, remove } from 'firebase/database';
import * as readline from 'readline';

// Firebase 配置
const firebaseConfig = {
  apiKey: "AIzaSyDWmByCJlz3zwo5TE8Vd2Ed4EKmWUlg0Qg",
  authDomain: "qmnoti.firebaseapp.com",
  projectId: "qmnoti",
  storageBucket: "qmnoti.appspot.com",
  messagingSenderId: "590755555605",
  appId: "1:590755555605:web:660cb1745c42231729211c",
  measurementId: "G-8F79PF308H",
  databaseURL: "https://qmnoti-default-rtdb.asia-southeast1.firebasedatabase.app/"
};

// 初始化 Firebase
const app = initializeApp(firebaseConfig);
const firestore = getFirestore(app);
const realtimeDB = getDatabase(app);

// 測試集合配置
const TEST_COLLECTIONS = {
  firestore: [
    'test_users',
    'test_alertEvents',
    'test_groups',
    'test_staff'
  ],
  realtimeDB: [
    'test_presence',
    'test_alertEvents',
    'test_stats'
  ]
};

interface CleanupOptions {
  dryRun: boolean;
  interactive: boolean;
  collections?: string[];
}

interface CleanupSummary {
  firestoreCollections: { [key: string]: number };
  realtimeDBPaths: { [key: string]: number };
  totalDeleted: number;
}

/**
 * 創建 readline 介面
 */
function createReadlineInterface() {
  return readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
}

/**
 * 詢問用戶確認
 */
async function askConfirmation(message: string): Promise<boolean> {
  const rl = createReadlineInterface();
  
  return new Promise((resolve) => {
    rl.question(`${message} (y/N): `, (answer) => {
      rl.close();
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

/**
 * 清理 Firestore 測試集合
 */
async function cleanupFirestoreCollection(collectionName: string, dryRun: boolean): Promise<number> {
  console.log(`🔍 檢查 Firestore 集合: ${collectionName}`);
  
  try {
    const collectionRef = collection(firestore, collectionName);
    const snapshot = await getDocs(collectionRef);
    
    if (snapshot.empty) {
      console.log(`   ✅ 集合 ${collectionName} 為空，無需清理`);
      return 0;
    }
    
    const docCount = snapshot.size;
    console.log(`   📊 找到 ${docCount} 個文檔`);
    
    if (dryRun) {
      console.log(`   🔍 [乾運行] 將刪除 ${docCount} 個文檔`);
      return docCount;
    }
    
    // 實際刪除文檔
    const deletePromises = snapshot.docs.map(docSnapshot => 
      deleteDoc(doc(firestore, collectionName, docSnapshot.id))
    );
    
    await Promise.all(deletePromises);
    console.log(`   ✅ 已刪除 ${docCount} 個文檔`);
    
    return docCount;
  } catch (error) {
    console.error(`   ❌ 清理集合 ${collectionName} 失敗:`, error);
    return 0;
  }
}

/**
 * 清理 Realtime Database 測試路徑
 */
async function cleanupRealtimeDBPath(path: string, dryRun: boolean): Promise<number> {
  console.log(`🔍 檢查 Realtime Database 路徑: ${path}`);
  
  try {
    const pathRef = ref(realtimeDB, path);
    const snapshot = await get(pathRef);
    
    if (!snapshot.exists()) {
      console.log(`   ✅ 路徑 ${path} 不存在或為空，無需清理`);
      return 0;
    }
    
    const data = snapshot.val();
    const itemCount = typeof data === 'object' ? Object.keys(data).length : 1;
    console.log(`   📊 找到 ${itemCount} 個項目`);
    
    if (dryRun) {
      console.log(`   🔍 [乾運行] 將刪除路徑 ${path} 及其所有數據`);
      return itemCount;
    }
    
    // 實際刪除路徑
    await remove(pathRef);
    console.log(`   ✅ 已刪除路徑 ${path} 及其 ${itemCount} 個項目`);
    
    return itemCount;
  } catch (error) {
    console.error(`   ❌ 清理路徑 ${path} 失敗:`, error);
    return 0;
  }
}

/**
 * 執行完整的測試集合清理
 */
async function cleanupTestCollections(options: CleanupOptions): Promise<CleanupSummary> {
  console.log('🧹 測試集合清理工具');
  console.log('=====================================');
  console.log(`模式: ${options.dryRun ? '🔍 乾運行（預覽模式）' : '🗑️  實際清理模式'}`);
  console.log(`項目: qmnoti`);
  console.log('');
  
  const summary: CleanupSummary = {
    firestoreCollections: {},
    realtimeDBPaths: {},
    totalDeleted: 0
  };
  
  // 清理 Firestore 測試集合
  console.log('📊 清理 Firestore 測試集合...');
  for (const collectionName of TEST_COLLECTIONS.firestore) {
    if (options.collections && !options.collections.includes(collectionName)) {
      continue;
    }
    
    const deletedCount = await cleanupFirestoreCollection(collectionName, options.dryRun);
    summary.firestoreCollections[collectionName] = deletedCount;
    summary.totalDeleted += deletedCount;
  }
  
  console.log('');
  
  // 清理 Realtime Database 測試路徑
  console.log('📊 清理 Realtime Database 測試路徑...');
  for (const path of TEST_COLLECTIONS.realtimeDB) {
    if (options.collections && !options.collections.includes(path)) {
      continue;
    }
    
    const deletedCount = await cleanupRealtimeDBPath(path, options.dryRun);
    summary.realtimeDBPaths[path] = deletedCount;
    summary.totalDeleted += deletedCount;
  }
  
  return summary;
}

/**
 * 顯示清理摘要
 */
function displayCleanupSummary(summary: CleanupSummary, options: CleanupOptions): void {
  console.log('\n📋 清理摘要');
  console.log('=====================================');
  
  console.log('\n🗃️  Firestore 集合:');
  Object.entries(summary.firestoreCollections).forEach(([collection, count]) => {
    console.log(`   - ${collection}: ${count} 個文檔`);
  });
  
  console.log('\n📊 Realtime Database 路徑:');
  Object.entries(summary.realtimeDBPaths).forEach(([path, count]) => {
    console.log(`   - ${path}: ${count} 個項目`);
  });
  
  console.log(`\n📊 總計: ${summary.totalDeleted} 個項目`);
  
  if (options.dryRun) {
    console.log('\n⚠️  這是乾運行模式，沒有實際刪除任何數據');
    console.log('   要執行實際清理，請使用: pnpm cleanup-test-collections --execute');
  } else {
    console.log('\n✅ 清理操作已完成');
  }
  
  console.log('=====================================\n');
}

/**
 * 解析命令行參數
 */
function parseArguments(): CleanupOptions {
  const args = process.argv.slice(2);
  
  const options: CleanupOptions = {
    dryRun: true,
    interactive: true
  };
  
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    switch (arg) {
      case '--execute':
      case '-e':
        options.dryRun = false;
        break;
      case '--dry-run':
      case '-d':
        options.dryRun = true;
        break;
      case '--no-interactive':
      case '-n':
        options.interactive = false;
        break;
      case '--collections':
      case '-c':
        const collections = args[i + 1]?.split(',');
        if (collections) {
          options.collections = collections;
          i++;
        }
        break;
      case '--help':
      case '-h':
        console.log(`
測試集合清理工具

用法:
  pnpm cleanup-test-collections [選項]

選項:
  --execute, -e              執行實際清理（默認為乾運行模式）
  --dry-run, -d              乾運行模式，只預覽不刪除（默認）
  --no-interactive, -n       非交互模式，跳過確認
  --collections, -c <列表>   指定要清理的集合（逗號分隔）
  --help, -h                 顯示此幫助信息

範例:
  pnpm cleanup-test-collections                    # 乾運行，預覽要刪除的數據
  pnpm cleanup-test-collections --execute          # 執行實際清理
  pnpm cleanup-test-collections -e -c test_users   # 只清理 test_users 集合
        `);
        process.exit(0);
        break;
    }
  }
  
  return options;
}

/**
 * 主函數
 */
async function main(): Promise<void> {
  try {
    const options = parseArguments();
    
    // 交互式確認
    if (options.interactive && !options.dryRun) {
      const confirmed = await askConfirmation(
        '⚠️  確定要清理所有測試集合嗎？這將刪除所有測試數據且無法撤銷！'
      );
      
      if (!confirmed) {
        console.log('❌ 用戶取消了清理操作');
        return;
      }
    }
    
    // 執行清理
    const summary = await cleanupTestCollections(options);
    
    // 顯示摘要
    displayCleanupSummary(summary, options);
    
    if (summary.totalDeleted === 0) {
      console.log('✅ 沒有找到需要清理的測試數據');
    }
    
  } catch (error) {
    console.error('❌ 清理過程中發生錯誤:', error);
    process.exit(1);
  }
}

// 運行腳本
if (require.main === module) {
  main().catch(console.error);
}

export { cleanupTestCollections, parseArguments };
