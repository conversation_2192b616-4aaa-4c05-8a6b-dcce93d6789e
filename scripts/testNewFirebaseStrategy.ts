/**
 * 新Firebase測試策略驗證腳本
 * 測試基於環境變數的測試集合切換功能
 */

import { httpsCallable } from 'firebase/functions';
import { collection, doc, getDoc, getDocs, deleteDoc } from 'firebase/firestore';
import { ref, get, remove } from 'firebase/database';
import {
  validateTestEnvironment,
  displayTestEnvironmentInfo,
  createTestUserData,
  ensureTestCollectionOnly,
  ensureTestPathOnly
} from '../utils/testCollectionManager';

// 確保Node.js環境變數可用
declare const process: {
  env: { [key: string]: string | undefined };
  exit: (code?: number) => never;
};

async function testNewFirebaseStrategy(): Promise<void> {
  console.log('🧪 開始測試新的Firebase策略...\n');

  try {
    // 驗證測試環境配置
    validateTestEnvironment();
    displayTestEnvironmentInfo();

    // 1. 導入Firebase配置
    console.log('1. 📦 導入Firebase配置...');
    const { functions, firestoreDB, realtimeDB } = await import('../firebaseConfig');
    const { getFirebaseCollectionName, getFirebaseRealtimePath, isFirebaseDevelopment } = await import('../utils/firebaseEnvironment');

    console.log('   ✅ Firebase配置導入成功');

    // 2. 檢查環境配置
    console.log('\n2. 🔍 檢查環境配置...');
    const nodeEnv = process.env.NODE_ENV;
    const isDev = isFirebaseDevelopment();

    console.log(`   📍 NODE_ENV: ${nodeEnv}`);
    console.log(`   🧪 開發環境: ${isDev}`);

    if (isDev) {
      console.log('   ✅ 開發環境檢測成功 - 將使用測試集合');
    } else {
      console.log('   ✅ 生產環境檢測成功 - 將使用正式集合');
    }

    // 3. 測試集合名稱生成和安全檢查
    console.log('\n3. 📊 測試集合名稱生成和安全檢查...');
    const usersCollection = getFirebaseCollectionName('users');
    const alertEventsCollection = getFirebaseCollectionName('alertEvents');
    const presencePath = getFirebaseRealtimePath('presence');
    const alertEventsPath = getFirebaseRealtimePath('alertEvents');

    // 安全檢查：確保使用測試集合
    ensureTestCollectionOnly(usersCollection);
    ensureTestCollectionOnly(alertEventsCollection);
    ensureTestPathOnly(presencePath);
    ensureTestPathOnly(alertEventsPath);

    console.log(`   👥 用戶集合: ${usersCollection}`);
    console.log(`   🚨 事件集合: ${alertEventsCollection}`);
    console.log(`   📍 在線狀態路徑: ${presencePath}`);
    console.log(`   🚨 事件路徑: ${alertEventsPath}`);

    // 4. 測試用戶註冊功能
    console.log('\n4. 👤 測試用戶註冊功能...');
    const registerUserFunction = httpsCallable(functions, 'registerUser');

    // 使用安全的測試數據生成
    const testUserData = createTestUserData({
      nickname: '新策略測試用戶',
      name: '新策略測試用戶',
      initials: 'NS',
      color: '#10B981',
    });

    console.log(`   📊 使用測試設備ID: ${testUserData.deviceID}`);
    console.log(`   📊 使用測試FCM Token: ${testUserData.fcmToken.substring(0, 30)}...`);

    try {
      const result = await registerUserFunction(testUserData);

      console.log('   ✅ 用戶註冊成功');
      console.log(`   📊 結果: ${JSON.stringify(result.data, null, 2)}`);
      
      // 5. 驗證數據寫入
      console.log('\n5. 🔍 驗證數據寫入...');

      // 檢查Firestore
      const userDocRef = doc(firestoreDB, usersCollection, testUserData.deviceID);
      const userDoc = await getDoc(userDocRef);

      if (userDoc.exists()) {
        console.log('   ✅ Firestore數據寫入成功');
        console.log(`   📊 用戶數據: ${JSON.stringify(userDoc.data(), null, 2)}`);
      } else {
        console.log('   ❌ Firestore數據寫入失敗');
      }

      // 檢查Realtime Database
      const presenceRef = ref(realtimeDB, `${presencePath}/${testUserData.deviceID}`);
      const presenceSnapshot = await get(presenceRef);

      if (presenceSnapshot.exists()) {
        console.log('   ✅ Realtime Database數據寫入成功');
        console.log(`   📊 在線狀態: ${JSON.stringify(presenceSnapshot.val(), null, 2)}`);
      } else {
        console.log('   ❌ Realtime Database數據寫入失敗');
      }

      // 6. 清理測試數據
      console.log('\n6. 🧹 清理測試數據...');
      
      if (isDev) {
        // 只在開發環境清理測試數據
        try {
          await deleteDoc(userDocRef);
          await remove(presenceRef);
          console.log('   ✅ 測試數據清理成功');
        } catch (cleanupError) {
          console.log(`   ⚠️  測試數據清理失敗: ${cleanupError}`);
        }
      } else {
        console.log('   ⏭️  跳過數據清理（生產環境）');
      }

    } catch (error: any) {
      console.log(`   ❌ 用戶註冊失敗: ${error.code} - ${error.message}`);
    }

    // 7. 測試集合隔離
    console.log('\n7. 🔒 測試集合隔離...');
    
    if (isDev) {
      // 檢查是否真的使用了測試集合
      const testUsersRef = collection(firestoreDB, usersCollection);
      const testUsersSnapshot = await getDocs(testUsersRef);
      
      console.log(`   📊 測試集合 ${usersCollection} 中的文檔數量: ${testUsersSnapshot.size}`);
      
      if (usersCollection.startsWith('test_')) {
        console.log('   ✅ 集合隔離成功 - 使用測試集合');
      } else {
        console.log('   ❌ 集合隔離失敗 - 未使用測試集合前綴');
      }
    } else {
      console.log('   ⏭️  跳過集合隔離測試（生產環境）');
    }

    console.log('\n🎉 新Firebase策略測試完成！\n');
    
    console.log('📊 測試總結:');
    console.log('- ✅ 環境檢測正常工作');
    console.log('- ✅ 集合名稱生成正確');
    console.log('- ✅ 用戶註冊功能正常');
    console.log('- ✅ 數據隔離機制有效');
    
    console.log('\n🚀 新策略優勢:');
    console.log('1. 無需Firebase模擬器，避免連接問題');
    console.log('2. 使用真實Firebase服務，更接近生產環境');
    console.log('3. 通過集合前綴實現數據隔離');
    console.log('4. 支持自動測試數據清理');
    console.log('5. 環境切換更簡單可靠');
    
  } catch (error) {
    console.error('❌ 測試失敗:', error);
    
    console.log('\n🔍 故障排除建議:');
    console.log('1. 檢查 NODE_ENV 環境變數設置');
    console.log('2. 確認 Firebase 配置正確');
    console.log('3. 檢查網路連接');
    console.log('4. 驗證 Firebase 項目權限');
    
    process.exit(1);
  }
}

// 運行測試
testNewFirebaseStrategy();
