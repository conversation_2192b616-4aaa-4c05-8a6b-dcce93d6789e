/**
 * Firebase 生產環境測試數據清理腳本
 * 用於安全地刪除測試過程中產生的測試數據
 */

import { initializeApp } from 'firebase/app';
import { getFirestore, collection, query, where, getDocs, deleteDoc, doc, Timestamp } from 'firebase/firestore';
import { getDatabase, ref, get, remove, child } from 'firebase/database';
import * as readline from 'readline';

// Firebase 配置
const firebaseConfig = {
  apiKey: "AIzaSyDWmByCJlz3zwo5TE8Vd2Ed4EKmWUlg0Qg",
  authDomain: "qmnoti.firebaseapp.com",
  projectId: "qmnoti",
  storageBucket: "qmnoti.appspot.com",
  messagingSenderId: "590755555605",
  appId: "1:590755555605:web:660cb1745c42231729211c",
  measurementId: "G-8F79PF308H",
  databaseURL: "https://qmnoti-default-rtdb.asia-southeast1.firebasedatabase.app/"
};

// 初始化 Firebase (生產環境)
const app = initializeApp(firebaseConfig);
const firestore = getFirestore(app);
const realtimeDB = getDatabase(app);

// 測試數據識別模式
const TEST_PATTERNS = {
  userDeviceId: /^prod_test_device_\d+$/,
  eventId: /^event_[a-z0-9]+_[a-z0-9]+$/,
  fcmToken: /^prod_test_fcm_token_\d+$/,
  testNickname: /^(Production Test User|Test User)$/
};

// 時間範圍（最近24小時的測試數據）
const TEST_TIME_RANGE = 24 * 60 * 60 * 1000; // 24小時（毫秒）

interface TestDataSummary {
  firestoreUsers: any[];
  firestoreEvents: any[];
  realtimePresence: any[];
  realtimeEvents: any[];
}

interface CleanupOptions {
  dryRun: boolean;
  timeRange?: number;
  interactive: boolean;
}

/**
 * 創建 readline 介面
 */
function createReadlineInterface() {
  return readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
}

/**
 * 詢問用戶確認
 */
async function askConfirmation(message: string): Promise<boolean> {
  const rl = createReadlineInterface();
  
  return new Promise((resolve) => {
    rl.question(`${message} (y/N): `, (answer) => {
      rl.close();
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

/**
 * 掃描 Firestore 中的測試用戶
 */
async function scanFirestoreUsers(timeRange: number): Promise<any[]> {
  console.log('🔍 掃描 Firestore 用戶數據...');
  
  const usersRef = collection(firestore, 'users');
  const snapshot = await getDocs(usersRef);
  const testUsers: any[] = [];
  
  const cutoffTime = Date.now() - timeRange;
  
  snapshot.forEach((doc) => {
    const data = doc.data();
    const docId = doc.id;
    
    // 檢查是否為測試數據
    const isTestUser = 
      TEST_PATTERNS.userDeviceId.test(docId) ||
      TEST_PATTERNS.userDeviceId.test(data.deviceID || '') ||
      TEST_PATTERNS.fcmToken.test(data.fcmToken || '') ||
      TEST_PATTERNS.testNickname.test(data.nickname || '');
    
    // 檢查創建時間
    let isRecentTest = false;
    if (data.createdAt) {
      const createdTime = data.createdAt.toMillis ? data.createdAt.toMillis() : data.createdAt;
      isRecentTest = createdTime > cutoffTime;
    }
    
    if (isTestUser || isRecentTest) {
      testUsers.push({
        id: docId,
        data: data,
        reason: isTestUser ? '匹配測試模式' : '最近創建的測試數據'
      });
    }
  });
  
  console.log(`   找到 ${testUsers.length} 個測試用戶記錄`);
  return testUsers;
}

/**
 * 掃描 Firestore 中的測試事件
 */
async function scanFirestoreEvents(timeRange: number): Promise<any[]> {
  console.log('🔍 掃描 Firestore 事件數據...');
  
  const eventsRef = collection(firestore, 'alertEvents');
  const snapshot = await getDocs(eventsRef);
  const testEvents: any[] = [];
  
  const cutoffTime = Date.now() - timeRange;
  
  snapshot.forEach((doc) => {
    const data = doc.data();
    const docId = doc.id;
    
    // 檢查是否為測試數據
    const isTestEvent = 
      TEST_PATTERNS.eventId.test(docId) ||
      TEST_PATTERNS.userDeviceId.test(data.initiatorDeviceID || '') ||
      (data.initiatorNickname && TEST_PATTERNS.testNickname.test(data.initiatorNickname));
    
    // 檢查創建時間
    let isRecentTest = false;
    if (data.createdAt) {
      const createdTime = data.createdAt.toMillis ? data.createdAt.toMillis() : data.createdAt;
      isRecentTest = createdTime > cutoffTime;
    }
    
    if (isTestEvent || isRecentTest) {
      testEvents.push({
        id: docId,
        data: data,
        reason: isTestEvent ? '匹配測試模式' : '最近創建的測試數據'
      });
    }
  });
  
  console.log(`   找到 ${testEvents.length} 個測試事件記錄`);
  return testEvents;
}

/**
 * 掃描 Realtime Database 中的測試數據
 */
async function scanRealtimeDatabase(timeRange: number): Promise<{ presence: any[], events: any[] }> {
  console.log('🔍 掃描 Realtime Database 數據...');
  
  const cutoffTime = Date.now() - timeRange;
  
  // 掃描 presence 數據
  const presenceRef = ref(realtimeDB, 'presence');
  const presenceSnapshot = await get(presenceRef);
  const testPresence: any[] = [];
  
  if (presenceSnapshot.exists()) {
    const presenceData = presenceSnapshot.val();
    Object.keys(presenceData).forEach(deviceId => {
      if (TEST_PATTERNS.userDeviceId.test(deviceId)) {
        testPresence.push({
          id: deviceId,
          data: presenceData[deviceId],
          reason: '匹配測試設備 ID'
        });
      }
    });
  }
  
  // 掃描 alertEvents 數據
  const alertEventsRef = ref(realtimeDB, 'alertEvents');
  const eventsSnapshot = await get(alertEventsRef);
  const testEvents: any[] = [];
  
  if (eventsSnapshot.exists()) {
    const eventsData = eventsSnapshot.val();
    Object.keys(eventsData).forEach(eventId => {
      const eventData = eventsData[eventId];
      const isTestEvent = 
        TEST_PATTERNS.eventId.test(eventId) ||
        TEST_PATTERNS.userDeviceId.test(eventData.initiatorDeviceID || '') ||
        (eventData.timestampCreated && eventData.timestampCreated > cutoffTime);
      
      if (isTestEvent) {
        testEvents.push({
          id: eventId,
          data: eventData,
          reason: '匹配測試模式或最近創建'
        });
      }
    });
  }
  
  console.log(`   找到 ${testPresence.length} 個測試 presence 記錄`);
  console.log(`   找到 ${testEvents.length} 個測試 alertEvents 記錄`);
  
  return { presence: testPresence, events: testEvents };
}

/**
 * 顯示測試數據摘要
 */
function displayTestDataSummary(summary: TestDataSummary): void {
  console.log('\n📊 測試數據摘要:');
  console.log('=====================================');
  
  console.log(`\n🗃️  Firestore 用戶 (${summary.firestoreUsers.length} 個):`);
  summary.firestoreUsers.forEach(user => {
    console.log(`   - ${user.id} (${user.data.nickname || 'N/A'}) - ${user.reason}`);
  });
  
  console.log(`\n📅 Firestore 事件 (${summary.firestoreEvents.length} 個):`);
  summary.firestoreEvents.forEach(event => {
    console.log(`   - ${event.id} (${event.data.initiatorNickname || 'N/A'}) - ${event.reason}`);
  });
  
  console.log(`\n👥 Realtime Database Presence (${summary.realtimePresence.length} 個):`);
  summary.realtimePresence.forEach(presence => {
    console.log(`   - ${presence.id} - ${presence.reason}`);
  });
  
  console.log(`\n🔔 Realtime Database Events (${summary.realtimeEvents.length} 個):`);
  summary.realtimeEvents.forEach(event => {
    console.log(`   - ${event.id} - ${event.reason}`);
  });
  
  const totalCount = summary.firestoreUsers.length + summary.firestoreEvents.length + 
                    summary.realtimePresence.length + summary.realtimeEvents.length;
  
  console.log(`\n📊 總計: ${totalCount} 個測試數據記錄`);
  console.log('=====================================\n');
}

/**
 * 執行 Firestore 數據清理
 */
async function cleanupFirestoreData(users: any[], events: any[], dryRun: boolean): Promise<void> {
  if (dryRun) {
    console.log('🔍 [乾運行] 模擬 Firestore 數據清理...');
    return;
  }

  console.log('🗑️  開始清理 Firestore 數據...');

  // 清理用戶數據
  for (const user of users) {
    try {
      await deleteDoc(doc(firestore, 'users', user.id));
      console.log(`   ✅ 已刪除用戶: ${user.id}`);
    } catch (error) {
      console.error(`   ❌ 刪除用戶失敗 ${user.id}:`, error);
    }
  }

  // 清理事件數據
  for (const event of events) {
    try {
      await deleteDoc(doc(firestore, 'alertEvents', event.id));
      console.log(`   ✅ 已刪除事件: ${event.id}`);
    } catch (error) {
      console.error(`   ❌ 刪除事件失敗 ${event.id}:`, error);
    }
  }

  console.log(`✅ Firestore 清理完成: ${users.length} 個用戶, ${events.length} 個事件`);
}

/**
 * 執行 Realtime Database 數據清理
 */
async function cleanupRealtimeData(presence: any[], events: any[], dryRun: boolean): Promise<void> {
  if (dryRun) {
    console.log('🔍 [乾運行] 模擬 Realtime Database 數據清理...');
    return;
  }

  console.log('🗑️  開始清理 Realtime Database 數據...');

  // 清理 presence 數據
  for (const presenceItem of presence) {
    try {
      await remove(ref(realtimeDB, `presence/${presenceItem.id}`));
      console.log(`   ✅ 已刪除 presence: ${presenceItem.id}`);
    } catch (error) {
      console.error(`   ❌ 刪除 presence 失敗 ${presenceItem.id}:`, error);
    }
  }

  // 清理事件數據
  for (const event of events) {
    try {
      await remove(ref(realtimeDB, `alertEvents/${event.id}`));
      console.log(`   ✅ 已刪除 alertEvent: ${event.id}`);
    } catch (error) {
      console.error(`   ❌ 刪除 alertEvent 失敗 ${event.id}:`, error);
    }
  }

  console.log(`✅ Realtime Database 清理完成: ${presence.length} 個 presence, ${events.length} 個事件`);
}

/**
 * 生成清理報告
 */
function generateCleanupReport(summary: TestDataSummary, options: CleanupOptions): void {
  const timestamp = new Date().toISOString();
  const totalCount = summary.firestoreUsers.length + summary.firestoreEvents.length +
                    summary.realtimePresence.length + summary.realtimeEvents.length;

  console.log('\n📋 清理報告');
  console.log('=====================================');
  console.log(`時間: ${timestamp}`);
  console.log(`模式: ${options.dryRun ? '乾運行（預覽）' : '實際清理'}`);
  console.log(`時間範圍: ${options.timeRange ? (options.timeRange / (60 * 60 * 1000)) + ' 小時' : '所有測試數據'}`);
  console.log(`\n清理統計:`);
  console.log(`  - Firestore 用戶: ${summary.firestoreUsers.length}`);
  console.log(`  - Firestore 事件: ${summary.firestoreEvents.length}`);
  console.log(`  - Realtime Presence: ${summary.realtimePresence.length}`);
  console.log(`  - Realtime Events: ${summary.realtimeEvents.length}`);
  console.log(`  - 總計: ${totalCount}`);

  if (options.dryRun) {
    console.log(`\n⚠️  這是乾運行模式，沒有實際刪除任何數據`);
    console.log(`   要執行實際清理，請使用: pnpm cleanup-test-data --execute`);
  } else {
    console.log(`\n✅ 清理操作已完成`);
  }
  console.log('=====================================\n');
}

/**
 * 主要清理函數
 */
async function cleanupTestData(options: CleanupOptions): Promise<void> {
  try {
    console.log('🧹 Firebase 生產環境測試數據清理工具');
    console.log('=====================================');
    console.log(`模式: ${options.dryRun ? '🔍 乾運行（預覽模式）' : '🗑️  實際清理模式'}`);
    console.log(`項目: qmnoti (生產環境)`);
    console.log(`時間範圍: ${options.timeRange ? (options.timeRange / (60 * 60 * 1000)) + ' 小時內' : '所有測試數據'}\n`);

    // 掃描所有測試數據
    const [firestoreUsers, firestoreEvents, realtimeData] = await Promise.all([
      scanFirestoreUsers(options.timeRange || TEST_TIME_RANGE),
      scanFirestoreEvents(options.timeRange || TEST_TIME_RANGE),
      scanRealtimeDatabase(options.timeRange || TEST_TIME_RANGE)
    ]);

    const summary: TestDataSummary = {
      firestoreUsers,
      firestoreEvents,
      realtimePresence: realtimeData.presence,
      realtimeEvents: realtimeData.events
    };

    // 顯示摘要
    displayTestDataSummary(summary);

    const totalCount = summary.firestoreUsers.length + summary.firestoreEvents.length +
                      summary.realtimePresence.length + summary.realtimeEvents.length;

    if (totalCount === 0) {
      console.log('✅ 沒有找到需要清理的測試數據');
      return;
    }

    // 交互式確認
    if (options.interactive && !options.dryRun) {
      const confirmed = await askConfirmation(
        `⚠️  確定要刪除這 ${totalCount} 個測試數據記錄嗎？這個操作無法撤銷！`
      );

      if (!confirmed) {
        console.log('❌ 用戶取消了清理操作');
        return;
      }
    }

    // 執行清理
    await Promise.all([
      cleanupFirestoreData(summary.firestoreUsers, summary.firestoreEvents, options.dryRun),
      cleanupRealtimeData(summary.realtimePresence, summary.realtimeEvents, options.dryRun)
    ]);

    // 生成報告
    generateCleanupReport(summary, options);

  } catch (error) {
    console.error('❌ 清理過程中發生錯誤:', error);
    process.exit(1);
  }
}

/**
 * 解析命令行參數
 */
function parseArguments(): CleanupOptions {
  const args = process.argv.slice(2);

  const options: CleanupOptions = {
    dryRun: true, // 默認為乾運行模式
    interactive: true
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    switch (arg) {
      case '--execute':
      case '-e':
        options.dryRun = false;
        break;
      case '--dry-run':
      case '-d':
        options.dryRun = true;
        break;
      case '--no-interactive':
      case '-n':
        options.interactive = false;
        break;
      case '--time-range':
      case '-t':
        const hours = parseInt(args[i + 1]);
        if (!isNaN(hours)) {
          options.timeRange = hours * 60 * 60 * 1000;
          i++; // 跳過下一個參數
        }
        break;
      case '--help':
      case '-h':
        console.log(`
Firebase 測試數據清理工具

用法:
  pnpm cleanup-test-data [選項]

選項:
  --execute, -e          執行實際清理（默認為乾運行模式）
  --dry-run, -d          乾運行模式，只預覽不刪除（默認）
  --no-interactive, -n   非交互模式，跳過確認
  --time-range, -t <小時> 指定時間範圍（小時）
  --help, -h             顯示此幫助信息

範例:
  pnpm cleanup-test-data                    # 乾運行，預覽要刪除的數據
  pnpm cleanup-test-data --execute          # 執行實際清理
  pnpm cleanup-test-data -e -t 12           # 清理最近12小時的測試數據
  pnpm cleanup-test-data -e -n              # 非交互模式執行清理
        `);
        process.exit(0);
        break;
    }
  }

  return options;
}

// 主程序入口
async function main(): Promise<void> {
  const options = parseArguments();
  await cleanupTestData(options);
}

// 運行腳本
if (require.main === module) {
  main().catch(console.error);
}

export { cleanupTestData, parseArguments };
