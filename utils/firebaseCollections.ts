/**
 * Firebase 集合名稱管理工具
 * 根據環境自動選擇測試集合或生產集合
 */

// 載入環境變數
import dotenv from 'dotenv';
dotenv.config();

/**
 * 集合配置介面
 */
interface CollectionConfig {
  firestore: {
    users: string;
    alertEvents: string;
    groups: string;
    staff: string;
  };
  realtimeDB: {
    presence: string;
    alertEvents: string;
    stats: string;
  };
}

/**
 * 判斷是否為開發環境
 */
const isDevelopment = process.env.NODE_ENV === 'development';

/**
 * 生產環境集合配置
 */
const productionCollections: CollectionConfig = {
  firestore: {
    users: 'users',
    alertEvents: 'alertEvents',
    groups: 'groups',
    staff: 'staff'
  },
  realtimeDB: {
    presence: 'presence',
    alertEvents: 'alertEvents',
    stats: 'stats'
  }
};

/**
 * 測試環境集合配置
 */
const testCollections: CollectionConfig = {
  firestore: {
    users: process.env.TEST_FIRESTORE_USERS_COLLECTION || 'test_users',
    alertEvents: process.env.TEST_FIRESTORE_EVENTS_COLLECTION || 'test_alertEvents',
    groups: process.env.TEST_FIRESTORE_GROUPS_COLLECTION || 'test_groups',
    staff: process.env.TEST_FIRESTORE_STAFF_COLLECTION || 'test_staff'
  },
  realtimeDB: {
    presence: process.env.TEST_REALTIME_PRESENCE_PATH || 'test_presence',
    alertEvents: process.env.TEST_REALTIME_EVENTS_PATH || 'test_alertEvents',
    stats: process.env.TEST_REALTIME_STATS_PATH || 'test_stats'
  }
};

/**
 * 獲取當前環境的集合配置
 */
export function getCollectionConfig(): CollectionConfig {
  const config = isDevelopment ? testCollections : productionCollections;
  
  // 在開發環境下輸出使用的集合名稱
  if (isDevelopment) {
    console.log('🧪 開發環境：使用測試集合');
    console.log('📊 Firestore 集合:');
    Object.entries(config.firestore).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`);
    });
    console.log('📈 Realtime Database 路徑:');
    Object.entries(config.realtimeDB).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`);
    });
  } else {
    console.log('🚀 生產環境：使用正式集合');
  }
  
  return config;
}

/**
 * 獲取 Firestore 集合名稱
 */
export function getFirestoreCollections() {
  return getCollectionConfig().firestore;
}

/**
 * 獲取 Realtime Database 路徑
 */
export function getRealtimeDatabasePaths() {
  return getCollectionConfig().realtimeDB;
}

/**
 * 獲取測試數據前綴
 */
export function getTestDataPrefix(): string {
  return process.env.TEST_DATA_PREFIX || 'test_';
}

/**
 * 檢查是否為測試數據
 */
export function isTestData(data: any): boolean {
  const prefix = getTestDataPrefix();
  
  // 檢查常見的測試數據標識
  if (typeof data === 'string') {
    return data.startsWith(prefix);
  }
  
  if (typeof data === 'object' && data !== null) {
    // 檢查 deviceID 或其他標識
    const deviceID = data.deviceID || data.id;
    if (deviceID && typeof deviceID === 'string') {
      return deviceID.startsWith(prefix);
    }
    
    // 檢查 nickname 或 name
    const nickname = data.nickname || data.name;
    if (nickname && typeof nickname === 'string') {
      return nickname.includes('Test') || nickname.includes('測試');
    }
  }
  
  return false;
}

/**
 * 生成測試設備 ID
 */
export function generateTestDeviceID(): string {
  const prefix = getTestDataPrefix();
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `${prefix}device_${timestamp}_${random}`;
}

/**
 * 生成測試事件 ID
 */
export function generateTestEventID(): string {
  const prefix = getTestDataPrefix();
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `${prefix}event_${timestamp}_${random}`;
}

/**
 * 環境信息
 */
export function getEnvironmentInfo() {
  return {
    isDevelopment,
    nodeEnv: process.env.NODE_ENV,
    collections: getCollectionConfig(),
    testPrefix: getTestDataPrefix()
  };
} 