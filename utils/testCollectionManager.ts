/**
 * 測試集合管理工具
 * 確保所有測試腳本都使用正確的測試集合，永不污染生產數據
 */

import { getFirebaseCollectionName, getFirebaseRealtimePath, isFirebaseDevelopment } from './firebaseEnvironment';

// 確保Node.js環境變數可用
declare const process: {
  env: { [key: string]: string | undefined };
  exit: (code?: number) => never;
};

/**
 * 測試環境配置
 */
export interface TestEnvironmentConfig {
  isDevelopment: boolean;
  nodeEnv: string;
  collections: {
    users: string;
    alertEvents: string;
    groups: string;
    staff: string;
  };
  realtimePaths: {
    presence: string;
    alertEvents: string;
    stats: string;
  };
}

/**
 * 測試數據生成配置
 */
export interface TestDataConfig {
  deviceIdPrefix: string;
  fcmTokenPrefix: string;
  nicknamePrefix: string;
  eventIdPrefix: string;
}

/**
 * 獲取測試環境配置
 */
export function getTestEnvironmentConfig(): TestEnvironmentConfig {
  const isDevelopment = isFirebaseDevelopment();
  const nodeEnv = process.env.NODE_ENV || 'development';
  
  return {
    isDevelopment,
    nodeEnv,
    collections: {
      users: getFirebaseCollectionName('users'),
      alertEvents: getFirebaseCollectionName('alertEvents'),
      groups: getFirebaseCollectionName('groups'),
      staff: getFirebaseCollectionName('staff'),
    },
    realtimePaths: {
      presence: getFirebaseRealtimePath('presence'),
      alertEvents: getFirebaseRealtimePath('alertEvents'),
      stats: getFirebaseRealtimePath('stats'),
    },
  };
}

/**
 * 獲取測試數據配置
 */
export function getTestDataConfig(): TestDataConfig {
  const isDevelopment = isFirebaseDevelopment();
  
  if (!isDevelopment) {
    throw new Error('❌ 禁止在生產環境中生成測試數據！請設置 NODE_ENV=development');
  }
  
  return {
    deviceIdPrefix: 'test_device_',
    fcmTokenPrefix: 'test_fcm_token_',
    nicknamePrefix: 'Test User',
    eventIdPrefix: 'test_event_',
  };
}

/**
 * 生成測試設備ID
 */
export function generateTestDeviceID(): string {
  const config = getTestDataConfig();
  return `${config.deviceIdPrefix}${Date.now()}`;
}

/**
 * 生成測試FCM Token
 */
export function generateTestFCMToken(): string {
  const config = getTestDataConfig();
  const randomSuffix = Math.random().toString(36).substring(2, 8);
  return `${config.fcmTokenPrefix}${Date.now()}_${randomSuffix}`;
}

/**
 * 生成測試用戶暱稱
 */
export function generateTestNickname(suffix?: string): string {
  const config = getTestDataConfig();
  const timestamp = Date.now();
  return suffix ? `${config.nicknamePrefix} ${suffix}` : `${config.nicknamePrefix} ${timestamp}`;
}

/**
 * 驗證是否為測試環境
 */
export function validateTestEnvironment(): void {
  const config = getTestEnvironmentConfig();
  
  console.log('🔍 驗證測試環境配置...');
  console.log(`   NODE_ENV: ${config.nodeEnv}`);
  console.log(`   開發環境: ${config.isDevelopment}`);
  
  if (!config.isDevelopment) {
    console.error('❌ 錯誤：當前不是開發環境！');
    console.error('   為了保護生產數據，測試腳本只能在開發環境下運行');
    console.error('   請設置: NODE_ENV=development');
    process.exit(1);
  }
  
  // 驗證集合名稱是否使用測試前綴
  const hasTestPrefix = Object.values(config.collections).every(name => name.startsWith('test_'));
  const hasTestPaths = Object.values(config.realtimePaths).every(path => path.startsWith('test_'));
  
  if (!hasTestPrefix || !hasTestPaths) {
    console.error('❌ 錯誤：集合配置未使用測試前綴！');
    console.error('   當前集合配置:', config.collections);
    console.error('   當前路徑配置:', config.realtimePaths);
    console.error('   所有測試集合和路徑都必須以 "test_" 開頭');
    process.exit(1);
  }
  
  console.log('✅ 測試環境驗證通過');
  console.log('   將使用以下測試集合:');
  Object.entries(config.collections).forEach(([key, value]) => {
    console.log(`     ${key}: ${value}`);
  });
  console.log('   將使用以下測試路徑:');
  Object.entries(config.realtimePaths).forEach(([key, value]) => {
    console.log(`     ${key}: ${value}`);
  });
  console.log('');
}

/**
 * 顯示測試環境信息
 */
export function displayTestEnvironmentInfo(): void {
  const config = getTestEnvironmentConfig();
  
  console.log('📊 測試環境信息');
  console.log('=====================================');
  console.log(`環境: ${config.isDevelopment ? '開發環境 (測試集合)' : '生產環境 (正式集合)'}`);
  console.log(`NODE_ENV: ${config.nodeEnv}`);
  console.log('');
  
  console.log('📋 集合配置:');
  console.log('Firestore 集合:');
  Object.entries(config.collections).forEach(([key, value]) => {
    console.log(`  ${key}: ${value}`);
  });
  console.log('Realtime Database 路徑:');
  Object.entries(config.realtimePaths).forEach(([key, value]) => {
    console.log(`  ${key}: ${value}`);
  });
  console.log('=====================================\n');
}

/**
 * 創建測試用戶數據
 */
export function createTestUserData(overrides?: Partial<any>): any {
  const config = getTestDataConfig();
  
  const defaultData = {
    deviceID: generateTestDeviceID(),
    nickname: generateTestNickname(),
    fcmToken: generateTestFCMToken(),
    name: generateTestNickname('User'),
    role: 'developer',
    initials: 'TU',
    color: '#10B981',
    phoneNumber: '+852 1234 5678',
    avatar: '',
  };
  
  return { ...defaultData, ...overrides };
}

/**
 * 創建測試通知數據
 */
export function createTestAlertData(initiatorDeviceID: string, recipientDeviceIDs: string[], overrides?: Partial<any>): any {
  const defaultData = {
    initiatorDeviceID,
    caseType: 'mother_baby_transfer',
    motherInitial: 'T',
    bedNumber: '101',
    designatedWard: 'Test Ward',
    clinicalNotes: '測試通知內容',
    recipientDeviceIDs,
  };
  
  return { ...defaultData, ...overrides };
}

/**
 * 檢查是否為測試數據
 */
export function isTestData(data: any): boolean {
  if (!data) return false;
  
  const testIndicators = [
    data.deviceID?.startsWith('test_device_'),
    data.fcmToken?.startsWith('test_fcm_token_'),
    data.nickname?.includes('Test User'),
    data.eventID?.startsWith('test_event_'),
  ];
  
  return testIndicators.some(indicator => indicator === true);
}

/**
 * 安全檢查：確保不會操作生產集合
 */
export function ensureTestCollectionOnly(collectionName: string): void {
  if (!collectionName.startsWith('test_')) {
    console.error(`❌ 安全檢查失敗：嘗試操作非測試集合 "${collectionName}"`);
    console.error('   測試腳本只能操作以 "test_" 開頭的集合');
    process.exit(1);
  }
}

/**
 * 安全檢查：確保不會操作生產路徑
 */
export function ensureTestPathOnly(path: string): void {
  if (!path.startsWith('test_')) {
    console.error(`❌ 安全檢查失敗：嘗試操作非測試路徑 "${path}"`);
    console.error('   測試腳本只能操作以 "test_" 開頭的路徑');
    process.exit(1);
  }
}
