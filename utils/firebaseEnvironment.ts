/**
 * Firebase 環境管理工具
 * 提供環境檢測和測試集合管理功能
 */

import { app, auth, firestoreDB, realtimeDB, functions } from '../firebaseConfig';
import { httpsCallable } from 'firebase/functions';

/**
 * Firebase 環境類型
 */
export type FirebaseEnvironment = 'development' | 'production';

/**
 * 環境配置介面
 */
export interface EnvironmentConfig {
  name: string;
  description: string;
  isDevelopment: boolean;
  collections: {
    users: string;
    alertEvents: string;
    groups: string;
    staff: string;
  };
  realtimePaths: {
    presence: string;
    alertEvents: string;
    stats: string;
  };
}

/**
 * 連接狀態介面
 */
export interface ConnectionStatus {
  isConnected: boolean;
  environment: FirebaseEnvironment;
  services: {
    firestore: boolean;
    database: boolean;
    functions: boolean;
    auth: boolean;
  };
  lastChecked: Date;
  error?: string;
}

/**
 * 環境配置映射
 */
const ENVIRONMENT_CONFIGS: Record<FirebaseEnvironment, EnvironmentConfig> = {
  development: {
    name: '開發環境 (測試集合)',
    description: '使用真實 Firebase 服務但連接到測試集合，確保數據隔離',
    isDevelopment: true,
    collections: {
      users: process.env.TEST_FIRESTORE_USERS_COLLECTION || 'test_users',
      alertEvents: process.env.TEST_FIRESTORE_EVENTS_COLLECTION || 'test_alertEvents',
      groups: process.env.TEST_FIRESTORE_GROUPS_COLLECTION || 'test_groups',
      staff: process.env.TEST_FIRESTORE_STAFF_COLLECTION || 'test_staff'
    },
    realtimePaths: {
      presence: process.env.TEST_REALTIME_PRESENCE_PATH || 'test_presence',
      alertEvents: process.env.TEST_REALTIME_EVENTS_PATH || 'test_alertEvents',
      stats: process.env.TEST_REALTIME_STATS_PATH || 'test_stats'
    }
  },
  production: {
    name: '生產環境',
    description: '使用真實的 Firebase 服務和正式集合，適合正式部署',
    isDevelopment: false,
    collections: {
      users: 'users',
      alertEvents: 'alertEvents',
      groups: 'groups',
      staff: 'staff'
    },
    realtimePaths: {
      presence: 'presence',
      alertEvents: 'alertEvents',
      stats: 'stats'
    }
  }
};

/**
 * Firebase 環境管理器類
 */
export class FirebaseEnvironmentManager {
  private static instance: FirebaseEnvironmentManager;
  private currentEnvironment: FirebaseEnvironment;
  private connectionStatus: ConnectionStatus | null = null;

  private constructor() {
    this.currentEnvironment = this.detectCurrentEnvironment();
  }

  /**
   * 獲取單例實例
   */
  public static getInstance(): FirebaseEnvironmentManager {
    if (!FirebaseEnvironmentManager.instance) {
      FirebaseEnvironmentManager.instance = new FirebaseEnvironmentManager();
    }
    return FirebaseEnvironmentManager.instance;
  }

  /**
   * 檢測當前環境
   * 根據 NODE_ENV 判斷使用測試集合還是生產集合
   */
  private detectCurrentEnvironment(): FirebaseEnvironment {
    const nodeEnv = process.env.NODE_ENV;

    if (nodeEnv === 'development') {
      return 'development';
    }
    if (nodeEnv === 'production') {
      return 'production';
    }

    // 默認為生產環境（安全考量）
    return 'production';
  }

  /**
   * 獲取當前環境
   */
  public getCurrentEnvironment(): FirebaseEnvironment {
    return this.currentEnvironment;
  }

  /**
   * 獲取環境配置
   */
  public getEnvironmentConfig(env?: FirebaseEnvironment): EnvironmentConfig {
    return ENVIRONMENT_CONFIGS[env || this.currentEnvironment];
  }

  /**
   * 檢查是否為開發環境（使用測試集合）
   */
  public isDevelopmentEnvironment(): boolean {
    return this.currentEnvironment === 'development';
  }

  /**
   * 檢查是否為生產環境
   */
  public isProductionEnvironment(): boolean {
    return this.currentEnvironment === 'production';
  }

  /**
   * 獲取集合名稱（根據環境返回測試或生產集合名稱）
   */
  public getCollectionName(baseCollectionName: keyof EnvironmentConfig['collections']): string {
    const config = this.getEnvironmentConfig();
    return config.collections[baseCollectionName];
  }

  /**
   * 獲取Realtime Database路徑（根據環境返回測試或生產路徑）
   */
  public getRealtimePath(basePathName: keyof EnvironmentConfig['realtimePaths']): string {
    const config = this.getEnvironmentConfig();
    return config.realtimePaths[basePathName];
  }

  /**
   * 檢查Firebase服務連接狀態
   */
  public async checkConnectionStatus(): Promise<ConnectionStatus> {
    const status: ConnectionStatus = {
      isConnected: false,
      environment: this.currentEnvironment,
      services: {
        firestore: false,
        database: false,
        functions: false,
        auth: false
      },
      lastChecked: new Date()
    };

    try {
      // 檢查 Functions 連接（通過健康檢查）
      try {
        const healthCheck = httpsCallable(functions, 'healthCheck');
        await healthCheck();
        status.services.functions = true;
      } catch (error) {
        console.warn('Functions 連接檢查失敗:', error);
      }

      // 檢查 Auth 連接
      try {
        await auth.authStateReady();
        status.services.auth = true;
      } catch (error) {
        console.warn('Auth 連接檢查失敗:', error);
      }

      // 檢查 Firestore 連接
      try {
        // 使用正確的 Firestore v9 API
        const { doc, getDoc } = await import('firebase/firestore');
        const testDoc = doc(firestoreDB, '_health_check', 'test');
        await getDoc(testDoc);
        status.services.firestore = true;
      } catch (error) {
        // 即使文檔不存在，如果能連接到Firestore也算成功
        if (error instanceof Error && !error.message.includes('offline')) {
          status.services.firestore = true;
        }
        console.warn('Firestore 連接檢查失敗:', error);
      }

      // 檢查 Realtime Database 連接
      try {
        // 使用正確的 Database v9 API
        const { ref, get } = await import('firebase/database');
        const testRef = ref(realtimeDB, '_health_check');
        await get(testRef);
        status.services.database = true;
      } catch (error) {
        console.warn('Database 連接檢查失敗:', error);
      }

      // 判斷整體連接狀態
      status.isConnected = Object.values(status.services).some(service => service);

    } catch (error) {
      status.error = error instanceof Error ? error.message : '未知錯誤';
      console.error('Firebase 連接狀態檢查失敗:', error);
    }

    this.connectionStatus = status;
    return status;
  }

  /**
   * 獲取最後的連接狀態
   */
  public getLastConnectionStatus(): ConnectionStatus | null {
    return this.connectionStatus;
  }

  /**
   * 獲取環境資訊摘要
   */
  public getEnvironmentSummary(): {
    environment: FirebaseEnvironment;
    config: EnvironmentConfig;
    status: ConnectionStatus | null;
  } {
    return {
      environment: this.currentEnvironment,
      config: this.getEnvironmentConfig(),
      status: this.connectionStatus
    };
  }

  /**
   * 記錄環境資訊到控制台
   */
  public logEnvironmentInfo(): void {
    const config = this.getEnvironmentConfig();
    const emoji = this.isDevelopmentEnvironment() ? '🧪' : '🚀';

    console.log(`${emoji} Firebase 環境: ${config.name}`);
    console.log(`📝 描述: ${config.description}`);
    console.log(`📊 集合配置:`);
    Object.entries(config.collections).forEach(([collection, name]) => {
      console.log(`   ${collection}: ${name}`);
    });
    console.log(`📊 Realtime Database 路徑:`);
    Object.entries(config.realtimePaths).forEach(([path, name]) => {
      console.log(`   ${path}: ${name}`);
    });

    if (this.connectionStatus) {
      console.log(`📊 連接狀態: ${this.connectionStatus.isConnected ? '✅ 已連接' : '❌ 未連接'}`);
      console.log(`🕐 最後檢查: ${this.connectionStatus.lastChecked.toLocaleString('zh-TW')}`);
    }
  }
}

/**
 * 獲取Firebase環境管理器實例
 */
export const getFirebaseEnvironmentManager = (): FirebaseEnvironmentManager => {
  return FirebaseEnvironmentManager.getInstance();
};

/**
 * 快速檢查當前環境
 */
export const getCurrentFirebaseEnvironment = (): FirebaseEnvironment => {
  return getFirebaseEnvironmentManager().getCurrentEnvironment();
};

/**
 * 快速檢查是否為開發環境（使用測試集合）
 */
export const isFirebaseDevelopment = (): boolean => {
  return getFirebaseEnvironmentManager().isDevelopmentEnvironment();
};

/**
 * 快速檢查是否為生產環境
 */
export const isFirebaseProduction = (): boolean => {
  return getFirebaseEnvironmentManager().isProductionEnvironment();
};

/**
 * 獲取集合名稱（根據環境自動選擇測試或生產集合）
 */
export const getFirebaseCollectionName = (baseCollectionName: keyof EnvironmentConfig['collections']): string => {
  return getFirebaseEnvironmentManager().getCollectionName(baseCollectionName);
};

/**
 * 獲取Realtime Database路徑（根據環境自動選擇測試或生產路徑）
 */
export const getFirebaseRealtimePath = (basePathName: keyof EnvironmentConfig['realtimePaths']): string => {
  return getFirebaseEnvironmentManager().getRealtimePath(basePathName);
};

/**
 * 快速檢查Firebase連接狀態
 */
export const checkFirebaseConnection = async (): Promise<ConnectionStatus> => {
  return await getFirebaseEnvironmentManager().checkConnectionStatus();
};
